# 角色

你是一位拥有15年经验的资深软件工程师和代码架构师，精通多种编程语言和软件设计模式。你的工作风格极其严谨、注重细节，并擅长编写清晰、准确的技术文档和进行深入的代码审查。

# 背景

我需要你执行一个完整的“代码重构与生成”任务。这个任务的核心目标是：首先深入理解并以文档形式精确记录我提供给你的源代码的全部功能与逻辑，然后基于这份文档重新生成一份功能完全一致的新代码，最后通过双重审查（文档对比和源码对比）来严格验证新代码的准确性和等价性。

# 任务指令

请严格按照以下四个阶段顺序执行任务，不要遗漏任何步骤。

### 准备工作：创建工作目录

在当前目录下，创建一个新的文件夹，用于存放本次任务生成的所有文件。文件夹名称为 `[请在此处填写新文件夹的名称]`。

---

### 阶段一：深入分析与文档生成 (Analysis & Documentation)

1.  **输入**: 在本指令末尾 `--- 在此下方粘贴您的原始代码 ---` 分割线之后提供的源代码 (下文称“原始代码”)。
2.  **任务**:
    * 请你逐行、逐函数、逐类地仔细阅读和分析给定的“原始代码”。
    * 你的目标是完全理解代码的每一个细节，包括但不限于：
        * **整体架构**: 代码的组织方式，采用了什么设计模式。
        * **文件/模块结构**: 每个文件的用途和核心职责。
        * **类与函数定义**: 每个函数/方法的核心功能、具体算法、输入参数和返回值。
        * **数据流与状态管理**: 数据是如何被创建、传递、修改和销毁的。
        * **外部依赖**: 代码依赖了哪些库、API或外部服务。
        * **执行入口与流程**: 程序从哪里开始执行，主要的调用链是怎样的。
3.  **输出**:
    * 在第一步创建的文件夹内，生成一个名为 `analysis_documentation.md` 的 Markdown 文件。
    * 这份文档必须 **极其详细和精确**，结构清晰，使用 Markdown 格式化，确保覆盖上述所有分析要点，不得有任何逻辑遗漏。

---

### 阶段二：基于文档的代码生成 (Code Generation)

1.  **输入**: 上一阶段生成的 `analysis_documentation.md` 文件。
2.  **任务**:
    * 现在，忘记所有“原始代码”。你唯一的信息来源就是 `analysis_documentation.md`。
    * 根据这份文档中描述的功能、逻辑和架构，重新编写一份全新的代码 (下文称“新代码”)。
    * 目标语言是 **`[请在此处指定目标语言，例如：MQL5, Python, C++]`**。
3.  **输出**:
    * 在同一个文件夹内，生成一个新的代码文件。
    * 文件名应为 `[请在此处填写生成代码的文件名，例如：generated_code.mq5]`。
    * “新代码”必须 **严格且完整地** 实现 `analysis_documentation.md` 中描述的所有功能。代码风格应保持专业、整洁、易于阅读，并包含必要的注释。

---

### 阶段三：审查第一部分 - 文档与新代码一致性验证 (Documentation vs. New Code Review)

1.  **输入**: `analysis_documentation.md` 文件 和 `[请在此处填写生成代码的文件名]` 文件 (“新代码”)。
2.  **任务**:
    * 扮演一个独立的代码审查员，批判性地比对文档和“新代码”，检查两者之间是否存在任何不一致。
    * 审查清单应至少包括：
        * **功能完整性**: 文档中描述的每个功能点是否都在“新代码”中得到了实现？
        * **逻辑准确性**: “新代码”中的算法和逻辑流是否与文档描述完全一致？
        * **接口一致性**: 函数的签名（参数、返回值）是否与文档定义相符？
        * **边界条件与错误处理**: “新代码”是否处理了文档中可能隐含的边界情况或错误？
3.  **输出**:
    * 在同一个文件夹内，创建一个名为 `review_report.md` 的审查报告文件。
    * 在该文件中，创建一个标题为 `## 第一部分：文档与新代码一致性审查` 的章节，并在下面记录本阶段的审查结果，包括发现的任何问题或改进建议。

---

### 阶段四：审查第二部分 - 新代码与原始代码等价性验证 (New Code vs. Original Code Review)

1.  **输入**: “原始代码” (位于本指令末尾) 和 “新代码” (`[请在此处填写生成代码的文件名]`)。
2.  **任务**:
    * 现在，直接对比“新代码”和“原始代码”。
    * 你的目标是确认两者在 **功能上是完全等价的**。这不仅仅是文本对比，而是深度的逻辑和行为对比。
    * 审查清单应至少包括：
        * **功能对等**: 对于相同的输入，两个版本的代码是否会产生完全相同的输出或行为？
        * **逻辑保留**: “原始代码”中的核心算法和关键业务逻辑是否在“新代码”中被准确无误地复现了？
        * **非功能性差异 (可选但推荐)**: “新代码”相比“原始代码”，在可读性、性能、健robustness或代码结构上是否有任何显著的改进或退步？
        * **无意引入的偏差**: “新代码”是否无意中引入了“原始代码”没有的副作用或删除了必要的功能？
3.  **输出**:
    * 打开 `review_report.md` 文件。
    * 在文件中，创建一个标题为 `## 第二部分：新旧代码功能等价性审查` 的章节。
    * 在该章节下，详细记录本阶段的对比分析结果。如果发现任何功能不等价的地方，请明确指出。最后，给出一个最终结论：“新代码在功能上与原始代码完全等价”或“新代码在功能上与原始代码存在差异，具体如下...”。

---

### 工作流程摘要

1.  创建名为 `[请在此处填写新文件夹的名称]` 的目录。
2.  分析本指令末尾的“原始代码”，生成 `analysis_documentation.md`。
3.  基于文档生成名为 `[请在此处填写生成代码的文件名]` 的“新代码”。
4.  创建 `review_report.md`，完成 **文档与新代码** 的一致性审查。
5.  在 `review_report.md` 中，继续完成 **新代码与原始代码** 的功能等价性审查。
6.  完成任务。

请现在开始执行。

--- 在此下方粘贴您的原始代码 ---

//@version=5
strategy("Gold Trade Setup Strategy", shorttitle="Gold Trade Setup", overlay=true)

// Inputs for AMA
length = input.int(title="AMA Length", defval=14, minval=1)
fastLength = input.int(title="Fast EMA Length", defval=2, minval=1)
slowLength = input.int(title="Slow EMA Length", defval=30, minval=1)
highlightMovements = input.bool(title="Highlight AMA Movements?", defval=true)
src = input.source(title="Source for AMA", defval=close)

// Inputs for Target and Stop Loss Levels
targetBox = input.float(title="Target Level Multiplier", defval=3.0, minval=1.0, step=0.1)
riskBox = input.float(title="Risk Level Multiplier", defval=1.0, minval=0.1, step=0.1)

// AMA Calculation
fastAlpha = 2 / (fastLength + 1)
slowAlpha = 2 / (slowLength + 1)

hh = ta.highest(src, length + 1)
ll = ta.lowest(src, length + 1)

mltp = hh - ll != 0 ? math.abs(2 * src - ll - hh) / (hh - ll) : 0.0

ssc = mltp * (fastAlpha - slowAlpha) + slowAlpha

var float ama = na
ama := na(ama[1]) ? src : ama[1] + math.pow(ssc, 2) * (src - ama[1])

amaColor = highlightMovements ? (ama > ama[1] ? color.green : color.red) : color.gray
plot(ama, title="AMA", linewidth=2, color=amaColor)

// Inputs for SuperTrend
atrPeriod = input.int(10, "ATR Length", minval=1)
factor = input.float(3.0, "Factor", minval=0.01, step=0.01)

[supertrend, direction] = ta.supertrend(factor, atrPeriod)

supertrend := barstate.isfirst ? na : supertrend
upTrend = plot(direction > 0 ? supertrend : na, "Up Trend", color=color.green, style=plot.style_linebr)
downTrend = plot(direction <= 0 ? supertrend : na, "Down Trend", color=color.red, style=plot.style_linebr)
bodyMiddle = plot(barstate.isfirst ? na : (open + close) / 2, "Body Middle", display=display.none)

fill(bodyMiddle, upTrend, color.new(color.green, 90), fillgaps=false)
fill(bodyMiddle, downTrend, color.new(color.red, 90), fillgaps=false)

// Buy Condition
var bool amaGreen = false
amaGreen := ama > ama[1] and close > ama ? true : amaGreen  // AMA green and price above AMA

buyCondition = amaGreen and direction > 0 and direction[1] <= 0  // SuperTrend turns green after AMA is green

if (buyCondition)
    entryPrice = close
    targetPrice = entryPrice + (entryPrice - low) * targetBox
    stopPrice = entryPrice - (entryPrice - low) * riskBox
    strategy.entry("SELL", strategy.short)


// Sell Condition
var bool amaRed = false
amaRed := ama < ama[1] and close < ama ? true : amaRed  // AMA red and price below AMA

sellCondition = amaRed and direction <= 0 and direction[1] > 0  // SuperTrend turns red after AMA is red

if (sellCondition)
    entryPrice = close
    targetPrice = entryPrice - (high - entryPrice) * targetBox
    stopPrice = entryPrice + (high - entryPrice) * riskBox
    strategy.entry("BUY", strategy.long)
    

// Alerts for SuperTrend
alertcondition(direction[1] > direction, title="Downtrend to Uptrend", message="The Supertrend value switched from Downtrend to Uptrend")
alertcondition(direction[1] < direction, title="Uptrend to Downtrend", message="The Supertrend value switched from Uptrend to Downtrend")
alertcondition(direction[1] != direction, title="Trend Change", message="The Supertrend value switched from Uptrend to Downtrend or vice versa")
// Moon Phase Calculation
var float moonCycle = 29.53  // Average days in a lunar cycle
var float newMoonBase = timestamp(2023, 1, 21, 0, 0)  // Known New Moon date (January 21, 2023)

daysSinceNewMoon = (timenow - newMoonBase) / (24 * 60 * 60 * 1000)
phase = daysSinceNewMoon % moonCycle  // Moon phase in days

isAmavasya = phase < 1 or phase > moonCycle - 1  // Near day 0 or end of the cycle
isPurnima = phase > moonCycle / 2 - 1 and phase < moonCycle / 2 + 1  // Near day 15

// Plot Amavasya and Purnima
bgcolor(isAmavasya ? color.new(color.black, 90) : na, title="Amavasya Highlight")
bgcolor(isPurnima ? color.new(color.white, 90) : na, title="Purnima Highlight")
